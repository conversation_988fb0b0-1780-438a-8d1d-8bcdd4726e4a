/**
 * @file CleanupCoordinator Enhanced Smoke Tests
 * @filepath shared/src/base/__tests__/CleanupCoordinatorEnhanced.smoke.test.ts
 * @task-id M-TSK-01.SUB-01.4.ENH-01.SMOKE
 * @component cleanup-coordinator-enhanced-smoke-tests
 * @reference foundation-context.MEMORY-SAFETY.003
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Smoke-Testing
 * @created 2025-07-23 03:03:24 +03
 * @modified 2025-07-23 03:03:24 +03
 */

import {
  CleanupCoordinatorEnhanced,
  createEnhancedCleanupCoordinator
} from '../CleanupCoordinatorEnhanced';
import {
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus
} from '../CleanupCoordinator';

// Simple smoke test without complex timer mocking
describe('CleanupCoordinatorEnhanced Smoke Tests', () => {
  let coordinator: CleanupCoordinatorEnhanced;

  beforeEach(async () => {
    coordinator = new CleanupCoordinatorEnhanced({
      testMode: true,
      templateValidationEnabled: true,
      rollbackEnabled: true,
      maxCheckpoints: 3,
      performanceMonitoringEnabled: false,
      phaseIntegrationEnabled: false
    });

    await coordinator.initialize();
  });

  afterEach(async () => {
    if (coordinator) {
      try {
        await coordinator.shutdown();
      } catch (error) {
        console.warn('Shutdown error in smoke test:', error);
      }
    }
  });

  it('should create enhanced coordinator instance', () => {
    expect(coordinator).toBeInstanceOf(CleanupCoordinatorEnhanced);
  });

  it('should register a simple template', () => {
    const template = {
      id: 'simple-template',
      name: 'Simple Test Template',
      description: 'Basic template for smoke testing',
      version: '1.0.0',
      author: 'smoke-test',
      createdAt: new Date(),
      modifiedAt: new Date(),
      tags: ['smoke'],
      operations: [{
        id: 'step1',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'test-component',
        operationName: 'smoke-cleanup',
        parameters: {},
        timeout: 1000,
        retryPolicy: {
          maxRetries: 0,
          retryDelay: 100,
          backoffMultiplier: 1.0,
          maxRetryDelay: 100,
          retryOnErrors: []
        },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 100,
        description: 'Smoke test step'
      }],
      conditions: [],
      rollbackSteps: [],
      metadata: {},
      validationRules: []
    };

    expect(() => coordinator.registerTemplate(template)).not.toThrow();
    
    const templates = coordinator.getTemplates();
    expect(templates).toHaveLength(1);
    expect(templates[0].id).toBe('simple-template');
  });

  it('should create and list checkpoints', async () => {
    const operationId = 'smoke-operation';
    const checkpointId = await coordinator.createCheckpoint(operationId, { smoke: true });
    
    expect(checkpointId).toBeDefined();
    expect(typeof checkpointId).toBe('string');
    
    const checkpoints = coordinator.listCheckpoints();
    expect(checkpoints).toHaveLength(1);
    expect(checkpoints[0].operationId).toBe(operationId);
  });

  it('should validate rollback capability', () => {
    const operationId = 'validation-test';
    
    // Before checkpoint
    const before = coordinator.validateRollbackCapability(operationId);
    expect(before.canRollback).toBe(false);
    
    // This is sufficient for smoke test - we know checkpoints work from previous test
  });

  it('should analyze simple dependencies', () => {
    const operations = [
      {
        id: 'op1',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'comp1',
        operation: async () => {},
        dependencies: [],
        createdAt: new Date()
      },
      {
        id: 'op2',
        type: CleanupOperationType.MEMORY_CLEANUP,
        priority: CleanupPriority.NORMAL,
        status: CleanupStatus.QUEUED,
        componentId: 'comp2',
        operation: async () => {},
        dependencies: ['op1'],
        createdAt: new Date()
      }
    ];

    const analysis = coordinator.analyzeDependencies(operations);
    expect(analysis).toBeDefined();
    expect(analysis.hasCycles).toBe(false);
    expect(analysis.criticalPath.length).toBeGreaterThan(0);
  });

  it('should work with factory function', () => {
    const factoryCoordinator = createEnhancedCleanupCoordinator({
      testMode: true,
      rollbackEnabled: false
    });
    
    expect(factoryCoordinator).toBeInstanceOf(CleanupCoordinatorEnhanced);
    
    // Don't forget to clean up
    factoryCoordinator.shutdown().catch(() => {
      // Ignore cleanup errors
    });
  });
}); 