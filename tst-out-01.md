oa-prod$ timeout 60s npm test -- CleanupCoordinatorEnhanced.test.ts  --testNamePattern="should execute templates with dependency resolution" --verbose sudo power

> oa-framework@1.0.0 test
> jest CleanupCoordinatorEnhanced.test.ts --testNamePattern=should execute templates with dependency resolution --verbose sudo power

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (7.926 s, 229 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✕ should execute templates with dependency resolution (24 ms)
      ○ skipped should register and validate cleanup templates
      ○ skipped should validate template structure and detect issues
      ○ skipped should filter templates by criteria
      ○ skipped should track template execution metrics
    Advanced Dependency Resolution
      ○ skipped should detect circular dependencies
      ○ skipped should build and analyze dependency graphs
      ○ skipped should optimize operation execution order
      ○ skipped should throw error for circular dependencies in optimization
      ○ skipped should identify bottlenecks and optimization opportunities
    Rollback and Recovery System
      ○ skipped should create and manage checkpoints
      ○ skipped should rollback to checkpoint successfully
      ○ skipped should rollback operation using most recent checkpoint
      ○ skipped should validate rollback capability
      ○ skipped should filter checkpoints by criteria
      ○ skipped should cleanup old checkpoints
      ○ skipped should handle rollback failures gracefully
    Integration and Performance
      ○ skipped should maintain backward compatibility with base CleanupCoordinator
      ○ skipped should handle template execution within performance requirements
      ○ skipped should handle dependency analysis within performance requirements
      ○ skipped should handle checkpoint creation within performance requirements
    Factory Functions
      ○ skipped should create enhanced cleanup coordinator via factory function
      ○ skipped should get enhanced cleanup coordinator via getter function
    Error Handling and Edge Cases
      ○ skipped should handle template execution with non-existent template
      ○ skipped should handle rollback with non-existent checkpoint
      ○ skipped should handle rollback with no checkpoints for operation
      ○ skipped should handle disabled rollback system
      ○ skipped should handle empty template operations
      ○ skipped should handle malformed component patterns

  ● CleanupCoordinatorEnhanced › Cleanup Templates System › should execute templates with dependency resolution

    expect(received).toBeLessThan(expected)

    Expected: < 0
    Received:   1

      276 |       expect(step1Index).toBeGreaterThanOrEqual(0);
      277 |       expect(step2Index).toBeGreaterThanOrEqual(0);
    > 278 |       expect(step1Index).toBeLessThan(step2Index);
          |                          ^
      279 |     }, 2000); // 2 second Jest timeout
      280 |
      281 |     it('should filter templates by criteria', () => {

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:278:26)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 28 skipped, 29 total
Snapshots:   0 total
Time:        8.861 s