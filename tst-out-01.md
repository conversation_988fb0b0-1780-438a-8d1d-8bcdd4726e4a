    Cleanup Templates System
      ✓ should register and validate cleanup templates (8 ms)
      ✓ should validate template structure and detect issues (54 ms)
      ✓ should execute templates with dependency resolution (5 ms)
      ✓ should filter templates by criteria (3 ms)
      ✕ should track template execution metrics (10005 ms)

    Rollback and Recovery System
      ✓ should create and manage checkpoints (9 ms)
      ✓ should rollback to checkpoint successfully (4 ms)
      ✕ should rollback operation using most recent checkpoint (10005 ms)
      ✓ should validate rollback capability (4 ms)
      ✓ should filter checkpoints by criteria (4 ms)
      ✕ should cleanup old checkpoints (5 ms)
      ✓ should handle rollback failures gracefully (4 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (8 ms)
      ✕ should handle template execution within performance requirements (3007 ms)
      ✓ should handle dependency analysis within performance requirements (5 ms)
      ✓ should handle checkpoint creation within performance requirements (7 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (53 ms)
      ✓ should handle rollback with non-existent checkpoint (9 ms)
      ✓ should handle rollback with no checkpoints for operation (2 ms)
      ✓ should handle disabled rollback system (3 ms)
      ✓ should handle empty template operations (4 ms)
      ✓ should handle malformed component patterns (3 ms)



  ● CleanupCoordinatorEnhanced › Cleanup Templates System › should track template execution metrics

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      392 |     });
      393 |
    > 394 |     it('should track template execution metrics', async () => {
          |     ^
      395 |       const template = {
      396 |         id: 'metrics-template',
      397 |         name: 'Metrics Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:394:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:99:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:28:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should rollback operation using most recent checkpoint

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      735 |     });
      736 |
    > 737 |     it('should rollback operation using most recent checkpoint', async () => {
          |     ^
      738 |       const operationId = 'test-operation-3';
      739 |
      740 |       // Create first checkpoint

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:737:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:682:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:28:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should cleanup old checkpoints

    expect(received).toBe(expected) // Object.is equality

    Expected: 3
    Received: 0

      807 |       // Cleanup checkpoints older than now (should remove all)
      808 |       const cleanedCount = await coordinator.cleanupCheckpoints(new Date());
    > 809 |       expect(cleanedCount).toBe(3);
          |                            ^
      810 |       expect(coordinator.listCheckpoints()).toHaveLength(0);
      811 |     });
      812 |

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:809:28)

  ● CleanupCoordinatorEnhanced › Integration and Performance › should handle template execution within performance requirements

    thrown: "Exceeded timeout of 3000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      883 |     }, 5000); // 5 second Jest timeout
      884 |
    > 885 |     it('should handle template execution within performance requirements', async () => {
          |     ^
      886 |       const template = {
      887 |         id: 'performance-template',
      888 |         name: 'Performance Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:885:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:849:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:28:1)

