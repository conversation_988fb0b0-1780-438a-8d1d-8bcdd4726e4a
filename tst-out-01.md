npm test -- --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinatorEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (25.128 s, 227 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✓ should register and validate cleanup templates (7 ms)
      ✓ should validate template structure and detect issues (52 ms)
      ✓ should execute templates with dependency resolution (5 ms)
      ✓ should filter templates by criteria (4 ms)
      ✕ should track template execution metrics (3007 ms)
    Advanced Dependency Resolution
      ✓ should create dependency graph without hanging (3 ms)
      ✓ should detect circular dependencies (4 ms)
      ✓ should build and analyze dependency graphs (9 ms)
      ✓ should optimize operation execution order (3 ms)
      ✓ should throw error for circular dependencies in optimization (3 ms)
      ✓ should identify bottlenecks and optimization opportunities (3 ms)
    Rollback and Recovery System
      ✓ should create and manage checkpoints (5 ms)
      ✓ should rollback to checkpoint successfully (3 ms)
      ✕ should rollback operation using most recent checkpoint (8005 ms)
      ✓ should validate rollback capability (4 ms)
      ✓ should filter checkpoints by criteria (3 ms)
      ✕ should cleanup old checkpoints (8006 ms)
      ✓ should handle rollback failures gracefully (9 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (5 ms)
      ✕ should handle template execution within performance requirements (4007 ms)
      ✓ should handle dependency analysis within performance requirements (6 ms)
      ✓ should handle checkpoint creation within performance requirements (7 ms)
    Factory Functions
      ✓ should create enhanced cleanup coordinator via factory function (3 ms)
      ✓ should get enhanced cleanup coordinator via getter function (3 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (4 ms)
      ✓ should handle rollback with non-existent checkpoint (3 ms)
      ✓ should handle rollback with no checkpoints for operation (4 ms)
      ✓ should handle disabled rollback system (6 ms)
      ✓ should handle empty template operations (4 ms)
      ✓ should handle malformed component patterns (5 ms)

  ● CleanupCoordinatorEnhanced › Cleanup Templates System › should track template execution metrics

    thrown: "Exceeded timeout of 3000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      395 |     });
      396 |
    > 397 |     it('should track template execution metrics', async () => {
          |     ^
      398 |       const template = {
      399 |         id: 'metrics-template',
      400 |         name: 'Metrics Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:397:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:102:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should rollback operation using most recent checkpoint

    thrown: "Exceeded timeout of 8000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      779 |     });
      780 |
    > 781 |     it('should rollback operation using most recent checkpoint', async () => {
          |     ^
      782 |       const operationId = 'test-operation-3';
      783 |
      784 |       try {

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:781:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:726:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should cleanup old checkpoints

    thrown: "Exceeded timeout of 8000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      865 |     });
      866 |
    > 867 |     it('should cleanup old checkpoints', async () => {
          |     ^
      868 |       const operationId = 'test-operation-7';
      869 |
      870 |       try {

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:867:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:726:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Integration and Performance › should handle template execution within performance requirements

    thrown: "Exceeded timeout of 4000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

       999 |     }, 5000); // 5 second Jest timeout
      1000 |
    > 1001 |     it('should handle template execution within performance requirements', async () => {
           |     ^
      1002 |       const template = {
      1003 |         id: 'performance-template',
      1004 |         name: 'Performance Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1001:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:965:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

Test Suites: 1 failed, 1 total
Tests:       4 failed, 26 passed, 30 total
Snapshots:   0 total
Time:        25.386 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.
