npm test -- --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinatorEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (18.135 s, 228 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✓ should register and validate cleanup templates (7 ms)
      ✓ should validate template structure and detect issues (56 ms)
      ✓ should execute templates with dependency resolution (5 ms)
      ✓ should filter templates by criteria (3 ms)
      ✕ should track template execution metrics (3006 ms)
    Advanced Dependency Resolution
      ✓ should create dependency graph without hanging (4 ms)
      ✓ should detect circular dependencies (11 ms)
      ✕ should build and analyze dependency graphs (7 ms)
      ✕ should optimize operation execution order (4 ms)
      ✓ should throw error for circular dependencies in optimization (4 ms)
      ✓ should identify bottlenecks and optimization opportunities (4 ms)
    Rollback and Recovery System
      ✓ should create and manage checkpoints (4 ms)
      ✓ should rollback to checkpoint successfully (3 ms)
      ✕ should rollback operation using most recent checkpoint (5006 ms)
      ✓ should validate rollback capability (3 ms)
      ✓ should filter checkpoints by criteria (3 ms)
      ✕ should cleanup old checkpoints (5005 ms)
      ✓ should handle rollback failures gracefully (4 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (4 ms)
      ✕ should handle template execution within performance requirements (3007 ms)
      ✕ should handle dependency analysis within performance requirements (5 ms)
      ✓ should handle checkpoint creation within performance requirements (9 ms)
    Factory Functions
      ✓ should create enhanced cleanup coordinator via factory function (4 ms)
      ✓ should get enhanced cleanup coordinator via getter function (4 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (3 ms)
      ✓ should handle rollback with non-existent checkpoint (4 ms)
      ✓ should handle rollback with no checkpoints for operation (5 ms)
      ✓ should handle disabled rollback system (2 ms)
      ✓ should handle empty template operations (3 ms)
      ✓ should handle malformed component patterns (4 ms)

  ● CleanupCoordinatorEnhanced › Cleanup Templates System › should track template execution metrics

    thrown: "Exceeded timeout of 3000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      395 |     });
      396 |
    > 397 |     it('should track template execution metrics', async () => {
          |     ^
      398 |       const template = {
      399 |         id: 'metrics-template',
      400 |         name: 'Metrics Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:397:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:102:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Advanced Dependency Resolution › should build and analyze dependency graphs

    expect(received).toContain(expected) // indexOf

    Expected value: "op4"
    Received array: ["op1", "op2", "op3"]

      665 |           expect(analysis.hasCycles).toBe(false);
      666 |           expect(analysis.criticalPath).toContain('op1');
    > 667 |           expect(analysis.criticalPath).toContain('op4');
          |                                         ^
      668 |           expect(analysis.parallelGroups.length).toBeGreaterThan(1);
      669 |           expect(analysis.estimatedExecutionTime).toBeGreaterThan(0);
      670 |

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:667:41
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:606:14)

  ● CleanupCoordinatorEnhanced › Advanced Dependency Resolution › should optimize operation execution order

    expect(received).toBeLessThan(expected)

    Expected: < 0
    Received:   1

      726 |           const op3Index = optimizedOrder.indexOf('op3');
      727 |
    > 728 |           expect(op1Index).toBeLessThan(op3Index);
          |                            ^
      729 |           expect(op2Index).toBeLessThan(op3Index);
      730 |
      731 |           clearTimeout(timeout);

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:728:28
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:682:14)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should rollback operation using most recent checkpoint

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      907 |     });
      908 |
    > 909 |     it('should rollback operation using most recent checkpoint', async () => {
          |     ^
      910 |       const operationId = 'test-operation-3';
      911 |
      912 |       // Create first checkpoint

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:909:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:854:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should cleanup old checkpoints

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      967 |     });
      968 |
    > 969 |     it('should cleanup old checkpoints', async () => {
          |     ^
      970 |       const operationId = 'test-operation-7';
      971 |
      972 |       try {

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:969:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:854:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Integration and Performance › should handle template execution within performance requirements

    thrown: "Exceeded timeout of 3000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1073 |     }, 5000); // 5 second Jest timeout
      1074 |
    > 1075 |     it('should handle template execution within performance requirements', async () => {
           |     ^
      1076 |       const template = {
      1077 |         id: 'performance-template',
      1078 |         name: 'Performance Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1075:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1039:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Integration and Performance › should handle dependency analysis within performance requirements

    Test mode: Too many operations for dependency analysis (10 > 5)

      1107 |         limit: 5
      1108 |       });
    > 1109 |       throw new Error(`Test mode: Too many operations for dependency analysis (${operations.length} > 5)`);
           |             ^
      1110 |     }
      1111 |
      1112 |     // CIRCUIT BREAKER #2: Execution time limit for test mode

      at CleanupCoordinatorEnhanced.analyzeDependencies (shared/src/base/CleanupCoordinatorEnhanced.ts:1109:13)
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1145:36)

Test Suites: 1 failed, 1 total
Tests:       7 failed, 23 passed, 30 total
Snapshots:   0 total
Time:        18.393 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.
